.page {
	background-image: url('../assets/login/bg.jpg');
	background-repeat: no-repeat;
	background-size: cover;
	height: 100%;
}
.container {
	height: inherit;
	
}
.panel {
	width: 100%;
	min-width: 800px;
	height: 80%;
	max-height: 80%;
	background-color: #fff;
	border-radius: 10px;
	padding: 50px 60px;
	.left {
		box-sizing: border-box;
		.logo {
			display: block;
			width: 80%;
			margin-bottom: 35px;
		}
		.big {
			display: block;
			width: 75%;
			height: auto;
		}
	}
	.right {
		.title-container{
			display: flex;
			margin-top: 10px;
			margin-bottom: 30px;
			h2{
				font-size: 28px;
				color: #2691FF;
				margin-top: 0;
				margin-bottom: 0;
			}
			span{
				font-size: 16px;
				margin-top: 12px;
				margin-left: 10px;
				color: #999;
			}
		}
		.row{
			margin-bottom: 20px;
			text-align: center;
		}
		.btn{
			width: 100%;
		}
		.link{
			font-size: 14px;
			color: #999;
			cursor: pointer;
			user-select: none; 
		}
		.qrCode-container{
			display: flex;
			margin-bottom: 30px;
			.ercode{
				margin-right: 0px;
			}

		}
	}
}
