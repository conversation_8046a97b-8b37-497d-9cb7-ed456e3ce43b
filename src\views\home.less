.home {
	display: flex;
	justify-content: center;
	padding-top: 160px;
	.left {
		
		h2 {
			font-size: 34px;
			color: #2f5664;
			font-weight: bold;
			letter-spacing: 1px;
			margin-top: 15px;
		}
		.desc {
			border-left: solid 5px #a6acad;
			padding-left: 15px;
			p {
				font-size: 24px;
				font-weight: bold;
				color: #a6acad;
				letter-spacing: 1px;
			}
		}
		.bottom {
			display: flex;
			justify-content: space-between;
			margin-top: 40px;
			.remark-container {
				.ball {
					width: 80px;
					height: 80px;
					border-radius: 40px;
					line-height: 80px;
					background-color: #666;
					font-size: 24px;
					text-align: center;
					color: #fff;
					font-weight: bold;
				}
				.blue {
					background-color: #26a6ff;
				}
				.red {
					background-color: #fe3950;
				}
				.green {
					background-color: #5ce055;
				}
				.remark {
					width: 100%;
					text-align: center;
					font-size: 16px;
					margin-top: 20px;
					color: #555;
				}
			}
		}
	}
	.right {

		.banner {
			width: 100%;
			height: auto;
		}
	}
}
