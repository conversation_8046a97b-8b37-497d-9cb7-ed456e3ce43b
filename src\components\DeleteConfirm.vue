<template>
    <el-dialog
        v-model="visible"
        title=""
        width="420px"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
        class="delete-confirm-dialog"
    >
        <div class="delete-confirm-content">
            <div class="icon-wrapper">
                <el-icon class="warning-icon" :size="48">
                    <WarningFilled />
                </el-icon>
            </div>
            <div class="message-wrapper">
                <h3 class="title">确认删除</h3>
                <p class="message">
                    确定要删除选中的记录吗？
                    <br />
                    <span class="count-text">共 {{ count }} 条记录</span>
                </p>
                <p class="warning-text">此操作不可恢复，请谨慎操作！</p>
            </div>
        </div>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel" size="large" class="cancel-btn">
                    取消
                </el-button>
                <el-button type="danger" @click="confirm" size="large" class="confirm-btn">
                    确认删除
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { WarningFilled } from '@element-plus/icons-vue'

export default {
    name: 'DeleteConfirm',
    components: {
        WarningFilled
    },
    data() {
        return {
            visible: false,
            count: 0,
            resolve: null,
            reject: null
        }
    },
    methods: {
        show(count = 1) {
            this.count = count
            this.visible = true
            
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        confirm() {
            this.visible = false
            if (this.resolve) {
                this.resolve()
            }
        },
        cancel() {
            this.visible = false
            if (this.reject) {
                this.reject()
            }
        }
    }
}
</script>

<style scoped>
.delete-confirm-dialog {
    border-radius: 12px;
}

.delete-confirm-dialog :deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.delete-confirm-dialog :deep(.el-dialog__header) {
    padding: 0;
    border-bottom: none;
}

.delete-confirm-dialog :deep(.el-dialog__body) {
    padding: 32px 24px 24px;
}

.delete-confirm-dialog :deep(.el-dialog__footer) {
    padding: 0 24px 32px;
    border-top: none;
}

.delete-confirm-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-wrapper {
    margin-bottom: 20px;
}

.warning-icon {
    color: #f56c6c;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.message-wrapper {
    width: 100%;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
}

.message {
    font-size: 16px;
    color: #606266;
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.count-text {
    font-weight: 600;
    color: #f56c6c;
}

.warning-text {
    font-size: 14px;
    color: #909399;
    margin: 0;
    font-style: italic;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.cancel-btn {
    min-width: 100px;
    border-color: #dcdfe6;
    color: #606266;
}

.cancel-btn:hover {
    border-color: #c0c4cc;
    color: #409eff;
}

.confirm-btn {
    min-width: 100px;
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
    box-shadow: 0 6px 16px rgba(245, 108, 108, 0.4);
    transform: translateY(-1px);
}

.confirm-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}
</style>
