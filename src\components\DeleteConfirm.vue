<template>
    <el-dialog
        v-model="visible"
        title=""
        width="420px"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
        class="delete-confirm-dialog"
    >
        <div class="delete-confirm-content">
            <div class="icon-wrapper">
                <el-icon class="warning-icon" :size="48">
                    <WarningFilled />
                </el-icon>
            </div>
            <div class="message-wrapper">
                <h3 class="title">确认删除</h3>
                <p class="message">
                    确定要删除选中的记录吗？
                    <br />
                    <span class="count-text">共 {{ count }} 条记录</span>
                </p>
                <p class="warning-text">此操作不可恢复，请谨慎操作！</p>
            </div>
        </div>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel" size="large" class="cancel-btn">
                    取消
                </el-button>
                <el-button type="danger" @click="confirm" size="large" class="confirm-btn">
                    确认删除
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { WarningFilled } from '@element-plus/icons-vue'

export default {
    name: 'DeleteConfirm',
    components: {
        WarningFilled
    },
    data() {
        return {
            visible: false,
            count: 0,
            resolve: null,
            reject: null
        }
    },
    methods: {
        show(count = 1) {
            this.count = count
            this.visible = true
            
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        confirm() {
            this.visible = false
            if (this.resolve) {
                this.resolve()
            }
        },
        cancel() {
            this.visible = false
            if (this.reject) {
                this.reject()
            }
        }
    }
}
</script>

<style scoped>
.delete-confirm-dialog {
    border-radius: 8px;
}

.delete-confirm-dialog :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(64, 158, 255, 0.12);
    border: 1px solid #e4e7ed;
}

.delete-confirm-dialog :deep(.el-dialog__header) {
    padding: 0;
    border-bottom: none;
}

.delete-confirm-dialog :deep(.el-dialog__body) {
    padding: 28px 24px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.delete-confirm-dialog :deep(.el-dialog__footer) {
    padding: 0 24px 28px;
    border-top: 1px solid #f0f2f5;
    background: #fafbfc;
}

.delete-confirm-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-wrapper {
    margin-bottom: 18px;
    padding: 12px;
    background: linear-gradient(135deg, #fff2e8 0%, #fef7f0 100%);
    border-radius: 50%;
    border: 2px solid #ffd4a3;
}

.warning-icon {
    color: #e6a23c;
    animation: gentle-pulse 3s infinite;
}

@keyframes gentle-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.message-wrapper {
    width: 100%;
}

.title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 14px 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.message {
    font-size: 15px;
    color: #5a6c7d;
    margin: 0 0 10px 0;
    line-height: 1.6;
}

.count-text {
    font-weight: 600;
    color: #409eff;
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.warning-text {
    font-size: 13px;
    color: #8492a6;
    margin: 0;
    font-style: italic;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 14px;
}

.cancel-btn {
    min-width: 90px;
    height: 36px;
    border: 1px solid #d3d4d6;
    color: #5a6c7d;
    background: #ffffff;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    border-color: #409eff;
    color: #409eff;
    background: #f0f7ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.15);
}

.confirm-btn {
    min-width: 90px;
    height: 36px;
    background: linear-gradient(135deg, #e6a23c 0%, #f0b90b 100%);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    color: #ffffff;
    box-shadow: 0 3px 8px rgba(230, 162, 60, 0.25);
    transition: all 0.3s ease;
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #f0b90b 0%, #e6a23c 100%);
    box-shadow: 0 5px 12px rgba(230, 162, 60, 0.35);
    transform: translateY(-1px);
}

.confirm-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
}
</style>
