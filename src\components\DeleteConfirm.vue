<template>
    <el-dialog
        v-model="visible"
        title=""
        width="400px"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
        class="delete-confirm-dialog"
    >
        <div class="delete-confirm-content">
            <div class="message-row">
                <el-icon class="warning-icon" :size="16">
                    <WarningFilled />
                </el-icon>
                <span class="message-text">确定要删除选中的记录?</span>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel" size="small" class="cancel-btn">
                    取消
                </el-button>
                <el-button type="primary" @click="confirm" size="small" class="confirm-btn">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { WarningFilled } from '@element-plus/icons-vue'

export default {
    name: 'DeleteConfirm',
    components: {
        WarningFilled
    },
    data() {
        return {
            visible: false,
            count: 0,
            resolve: null,
            reject: null
        }
    },
    methods: {
        show(count = 1) {
            this.count = count
            this.visible = true
            
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        confirm() {
            this.visible = false
            if (this.resolve) {
                this.resolve()
            }
        },
        cancel() {
            this.visible = false
            if (this.reject) {
                this.reject()
            }
        }
    }
}
</script>

<style scoped>
.delete-confirm-dialog :deep(.el-dialog) {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.delete-confirm-dialog :deep(.el-dialog__header) {
    display: none;
}

.delete-confirm-dialog :deep(.el-dialog__body) {
    padding: 24px 24px 16px;
    color: #606266;
}

.delete-confirm-dialog :deep(.el-dialog__footer) {
    padding: 0 24px 24px;
    text-align: right;
}

.delete-confirm-content {
    display: flex;
    flex-direction: column;
}

.message-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.warning-icon {
    color: #e6a23c;
    flex-shrink: 0;
}

.message-text {
    font-size: 14px;
    color: #606266;
    line-height: 1.4;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.cancel-btn {
    min-width: 64px;
    font-size: 14px;
}

.confirm-btn {
    min-width: 64px;
    font-size: 14px;
}
</style>
