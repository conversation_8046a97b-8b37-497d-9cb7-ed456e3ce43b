<template>
    <el-dialog
        v-model="visible"
        title="提示"
        width="416px"
        :show-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
        class="delete-confirm-dialog"
    >
        <div class="delete-confirm-content">
            <div class="message-row">
                <el-icon class="warning-icon" :size="20">
                    <WarningFilled />
                </el-icon>
                <span class="message-text">确定要删除选中的记录?</span>
            </div>
            <div class="count-row" v-if="count > 1">
                <span class="count-text">共 {{ count }} 条记录</span>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel" class="cancel-btn">
                    取消
                </el-button>
                <el-button type="primary" @click="confirm" class="confirm-btn">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { WarningFilled } from '@element-plus/icons-vue'

export default {
    name: 'DeleteConfirm',
    components: {
        WarningFilled
    },
    data() {
        return {
            visible: false,
            count: 0,
            resolve: null,
            reject: null
        }
    },
    methods: {
        show(count = 1) {
            this.count = count
            this.visible = true
            
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        confirm() {
            this.visible = false
            if (this.resolve) {
                this.resolve()
            }
        },
        cancel() {
            this.visible = false
            if (this.reject) {
                this.reject()
            }
        }
    }
}
</script>

<style scoped>
.delete-confirm-dialog :deep(.el-dialog) {
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-confirm-dialog :deep(.el-dialog__header) {
    padding: 20px 20px 0;
    border-bottom: none;
}

.delete-confirm-dialog :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

.delete-confirm-dialog :deep(.el-dialog__body) {
    padding: 20px;
    color: #606266;
}

.delete-confirm-dialog :deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    text-align: right;
}

.delete-confirm-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.message-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.warning-icon {
    color: #e6a23c;
    margin-top: 2px;
    flex-shrink: 0;
}

.message-text {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
}

.count-row {
    margin-left: 28px;
}

.count-text {
    font-size: 14px;
    color: #909399;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancel-btn {
    min-width: 78px;
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
}

.confirm-btn {
    min-width: 78px;
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
}
</style>
