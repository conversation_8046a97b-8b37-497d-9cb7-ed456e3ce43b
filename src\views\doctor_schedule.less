.form {
	margin-right: 0 !important;
	margin-bottom: 5px;
}
.mold {
	float: right;
	margin-right: 0 !important;
}

.blue {
	color: #409eff;
	font-size: 18px;
	cursor: pointer;
	user-select: none;
}
.red {
	color: #f56c6c;
	font-size: 14px;
	cursor: pointer;
	user-select: none;
}
.schedule {
	display: table;
	width: 100%;
	border: solid 1px #e0e0e0;
	border-collapse: collapse;
	border-spacing: none;
	cursor: default;
	.row {
		display: table-row;
		&:first-child {
			// font-weight: bold;
			background-color: #6f768e !important;
			color: #fff;
		}
		&:nth-child(odd) {
			background-color: #f5f5f5;
		}
		.cell-header {
			display: table-cell;
			text-align: center;
			height: 38px;
			border: 0;
			padding: 0;
			box-sizing: border-box;
			position: relative;
			vertical-align: middle;
			border: solid 1px #e0e0e0;
		}
		.name {
			width: 130px;
		}
		.time {
			text-align: center;
			font-size: 12px;
		}
		.operate {
			display: table-cell;
			text-align: center;
			height: 38px;
			width: 105px;
		}
		.cell {
			display: table-cell;
			text-align: center;
			height: 36px;
			border: solid 1px #e0e0e0;
			padding: 0;
			box-sizing: border-box;
			position: relative;
			vertical-align: middle;
			font-size: 14px;
		}
	}
}
