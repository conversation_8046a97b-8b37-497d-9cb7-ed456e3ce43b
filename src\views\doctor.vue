<template>
    <div v-if="isAuth(['ROOT', 'DOCTOR:SELECT'])">
        <el-form :inline="true" :model="dataForm" :rules="dataRule" ref="dataForm">
            <el-form-item prop="name">
                <el-input
                    v-model="dataForm.name"
                    placeholder="姓名"
                    size="medium"
                    class="input"
                    clearable="clearable"
                />
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="dataForm.deptId"
                    class="input"
                    placeholder="科室"
                    size="medium"
                    clearable="clearable"
                >
                    <el-option v-for="one in medicalDeptList" :label="one.name" :value="one.id" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="dataForm.degree"
                    class="input"
                    placeholder="学历"
                    size="medium"
                    clearable="clearable"
                >
                    <el-option label="博士" value="博士" />
                    <el-option label="研究生" value="研究生" />
                    <el-option label="本科" value="本科" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.job" class="input" placeholder="职位" size="medium" clearable="clearable">
                    <el-option label="主任医师" value="主任医师" />
                    <el-option label="副主任医师" value="副主任医师" />
                    <el-option label="主治医师" value="主治医师" />
                    <el-option label="副主治医师" value="副主治医师" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.recommended" class="input" placeholder="推荐级别" clearable="clearable">
                    <el-option label="优先" value="true" />
                    <el-option label="非优先" value="false" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="medium" type="primary" @click="searchHandle()">查询</el-button>
                <el-button
                    size="medium"
                    type="primary"
                    :disabled="!isAuth(['ROOT', 'DOCTOR:INSERT'])"
                    @click="addHandle()"
                >
                    新增
                </el-button>
                <el-button
                    size="medium"
                    type="danger"
                    :disabled="!isAuth(['ROOT', 'DOCTOR:DELETE'])"
                    @click="deleteHandle()"
                >
                    批量删除
                </el-button>
            </el-form-item>
            <div style="float: right">
                <el-radio-group v-model="dataForm.status" @change="searchHandle()">
                    <el-radio-button label="在职" />
                    <el-radio-button label="离职" />
                    <el-radio-button label="退休" />
                </el-radio-group>
            </div>
        </el-form>
        <el-table
            :data="dataList"
            border
            v-loading="dataListLoading"
            :cell-style="{ padding: '3px 0' }"
            style="width: 100%;"
            size="medium"
            @selection-change="selectionChangeHandle"
            @expand-change="expand"
            :row-key="getRowKeys"
            :expand-row-keys="expands"
            @sort-change="orderHandle"
        >
            <el-table-column type="expand">
                <template #default="scope">
                    <div>
                        <table class="content">
                            <tr>
                                <th width="140">身份证号</th>
                                <td width="320">{{ content.pid }}</td>
                                <th width="140">出生日期</th>
                                <td width="320">{{ content.birthday }}</td>
                                <td width="110" rowspan="3" align="center">
                                    <el-upload
                                        class="avatar-uploader"
                                        :action="action"
                                        :headers="{ token: token }"
                                        with-credentials="true"
                                        :on-success="updatePhotoSuccess"
                                        :on-error="updatePhotoError"
                                        :show-file-list="false"
                                        :data="{ doctorId: scope.row.id }"
                                    >
                                        <el-image style="width: 100px; height: 100px" :src="content.photo" :fit="fit">
                                            <template #error>
                                                <div class="error-img">
                                                    <el-icon><Picture /></el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                    </el-upload>
                                </td>
                            </tr>
                            <tr>
                                <th>医师编号</th>
                                <td>{{ content.uuid }}</td>
                                <th>入职日期</th>
                                <td>{{ content.hiredate }}</td>
                            </tr>
                            <tr>
                                <th>电子信箱</th>
                                <td>{{ content.email }}</td>
                                <th>备注信息</th>
                                <td>{{ content.remark }}</td>
                            </tr>
                            <tr>
                                <th>标签描述</th>
                                <td>
                                    <el-tag v-for="one of content.tag">{{ one }}</el-tag>
                                </td>
                                <th>家庭住址</th>
                                <td colspan="2">{{ content.address }}</td>
                            </tr>
                            <tr>
                                <th>介绍信息</th>
                                <td colspan="4">{{ content.description }}</td>
                            </tr>
                        </table>
                    </div>
                </template>
            </el-table-column>
            <el-table-column type="selection" header-align="center" align="center" width="50" />
            <el-table-column type="index" header-align="center" align="center" width="100" label="序号">
                <template #default="scope">
                    <span>{{ (pageIndex - 1) * pageSize + scope.$index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="name"
                header-align="center"
                align="center"
                min-width="120"
                label="姓名"
                :show-overflow-tooltip="true"
            />
            <el-table-column prop="sex" header-align="center" align="center" min-width="70" label="性别" />
            <el-table-column prop="tel" header-align="center" align="center" min-width="120" label="电话" />
            <el-table-column prop="job" header-align="center" align="center" min-width="100" label="职务" />
            <el-table-column
                prop="deptName"
                header-align="center"
                align="center"
                min-width="120"
                label="科室"
                :show-overflow-tooltip="true"
                sortable
            />
            <el-table-column
                prop="subName"
                header-align="center"
                align="center"
                min-width="120"
                label="诊室"
                :show-overflow-tooltip="true"
            />
            <el-table-column
                prop="school"
                header-align="center"
                align="center"
                min-width="170"
                label="毕业学校"
                :show-overflow-tooltip="true"
            />
            <el-table-column prop="degree" header-align="center" align="center" min-width="100" label="学历" />
            <el-table-column prop="status" header-align="center" align="center" min-width="80" label="状态" />
            <el-table-column header-align="center" align="center" width="150" label="操作">
                <template #default="scope">
                    <el-button
                        type="text"
                        size="medium"
                        :disabled="!isAuth(['ROOT', 'DOCTOR:UPDATE'])"
                        @click="updateHandle(scope.row.id)"
                    >
                        修改
                    </el-button>
                    <el-button
                        type="text"
                        size="medium"
                        :disabled="!isAuth(['ROOT', 'DOCTOR:DELETE'])"
                        @click="deleteHandle(scope.row.id)"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50]"
            :page-size="pageSize"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <add-or-update ref="addOrUpdate" @refreshDataList="loadDataList"></add-or-update>
        <delete-confirm ref="deleteConfirm"></delete-confirm>
    </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus';
import AddOrUpdate from './doctor-add-or-update.vue';
import DeleteConfirm from '../components/DeleteConfirm.vue';
export default {
    components: {
        AddOrUpdate,
        DeleteConfirm
    },
    data() {
        return {
            token: localStorage.getItem('token'),
            action: `${this.$baseUrl}/doctor/updatePhoto`,
            dataForm: {
                name: '',
                deptId: '',
                degree: '',
                job: '',
                recommended: '',
                status: '在职',
                order: null
            },
            dataList: [],
            medicalDeptList: [],
            pageIndex: 1,
            pageSize: 10,
            totalCount: 0,
            dataListLoading: false,
            dataListSelections: [],
            dataRule: {
                name: [{ required: false, pattern: '^[\u4e00-\u9fa5]{1,10}$', message: '姓名格式错误' }]
            },
            expands: [],
            getRowKeys(row) {
                return row.id;
            },
            content: {
                id: null,
                photo: '',
                pid: '',
                birthday: '',
                uuid: '',
                hiredate: '',
                email: '',
                remark: '',
                tag: '',
                address: '',
                description: ''
            }
        };
    },
    methods: {
        // 方法1：加载数据列表
        loadDataList: function() {
            let that = this
            that.dataListLoading = true
            // 💡 修正了变量名拼写，并修正了对象中的逗号位置
            let json = { 在职: 1, 离职: 2, 退休: 3 }; 
            let data = {
                page: that.pageIndex,
                length: that.pageSize,
                name: that.dataForm.name == '' ? null : that.dataForm.name,
                deptId: that.dataForm.deptId == '' ? null : that.dataForm.deptId,
                degree: that.dataForm.degree == '' ? null : that.dataForm.degree,
                job: that.dataForm.job == '' ? null : that.dataForm.job,
                recommended: that.dataForm.recommended == '' ? null : that.dataForm.recommended,
                status: json[that.dataForm.status], // 使用修正后的变量名
                order: that.dataForm.order
            };
            that.$http('/doctor/searchByPage', 'POST', data, true, function(resp){
                let result = resp.result
                let temp = {
                    '1': '在职',
                    '2': '离职',
                    '3': '退休'
                };
                for (let one of result.list) {
                    one.status = temp[one.status + ''];
                }
                that.dataList = result.list;
                that.totalCount = result.totalCount;
                that.dataListLoading = false;
            })
        }, // 💡 每个方法结束后用逗号隔开
    
        // 方法2：加载科室列表 (移入 methods)
        loadMedicalDeptList: function() {
            let that = this;
            console.log('开始加载科室列表...');
            that.$http('/medical/dept/searchAll', 'GET', {}, true, function(resp) {
                console.log('科室列表响应:', resp);
                that.medicalDeptList = resp.result;
                console.log('科室列表数据:', that.medicalDeptList);
            });
        }, // 💡 每个方法结束后用逗号隔开
    
        // 方法3：每页条数改变 (移入 methods)
        sizeChangeHandle: function(val) {
            this.pageSize = val;
            this.pageIndex = 1;
            this.loadDataList();
        }, // 💡 每个方法结束后用逗号隔开
    
        // 方法4：当前页码改变 (移入 methods)
        currentChangeHandle: function(val) {
            this.pageIndex = val;
            this.loadDataList();
        },
		searchHandle:function(param){
			this.$refs['dataForm'].validate(valid => {
				if(valid){
					this.$refs['dataForm'].clearValidate();
					if(this.pageIndex!=1){
						this.pageIndex=1;
					}
					this.loadDataList();
				}else
				{
					return false;
				}
			})
		},
		expand: function(row, expandedRows) {
		    let that = this
		    if (expandedRows.length > 0) {
		        that.expands = [];
		        that.expands.push(row.id);
		        let data = {
		            id: row.id
		        };
		        that.$http('/doctor/searchContent', 'POST', data, false, function(resp) {
		            that.content.id = row.id;
		            that.content.photo = `${that.$minioUrl}${resp.photo}?random=${Math.random()}`;
		            that.content.pid = resp.pid;
		            that.content.birthday = resp.birthday;
		            that.content.uuid = resp.uuid;
		            that.content.hiredate = resp.hiredate;
		            that.content.email = resp.email;
		            that.content.remark = resp.remark;
		            that.content.tag = resp.tag;
		            that.content.address = resp.address;
		            that.content.description = resp.description;
		        });
		    } else {
		        that.expands = [];
		    }
		},
		updatePhotoSuccess:function(){
			this.content.photo=`${this.$minioUrl}/doctor/doctor-${this.content.id}.jpg?random=${Math.random()}`;
		},
		updatePhotoError:function(){
			ElMessage({
				message: '文件上传失败',
				type: 'error',
				duration: 1200
			});
		},
		addHandle: function() {
		  this.$nextTick(() => {
		    this.$refs.addOrUpdate.init()
		  })
		},
		updateHandle: function(id) {
		  this.$nextTick(() => {
		    this.$refs.addOrUpdate.init(id)
		  })
		},deleteHandle: function(id) {
    let that = this;
    let ids = id ? [id] : that.dataListSelections.map(item => {
        return item.id;
    });

    if (ids.length == 0) {
        ElMessage({
            message: '没有选中记录',
            type: 'warning',
            duration: 1200
        });
        return;
    } else {
        this.$refs.deleteConfirm.show(ids.length).then(() => {
            // 使用正确的删除接口名称
            let deleteUrl = '/doctor/deleteByIds';
            let deleteMethod = 'POST';
            
            console.log('准备删除医生，IDs:', ids);
            console.log('请求URL:', that.$baseUrl + deleteUrl);
            
            // 使用标准的删除接口
            that.$http(deleteUrl, deleteMethod, { ids: ids }, true, function(resp) {
                console.log('删除成功响应:', resp);
                ElMessage({
                    message: '操作成功',
                    type: 'success',
                    duration: 1200,
                    onClose: () => {
                        that.loadDataList();
                    }
                });
            });
        }).catch(() => {
            // 用户取消删除操作
            console.log('用户取消删除操作');
        });
    }
},

// 添加一个备用的删除方法，尝试逐个删除
deleteHandleAlternative: function(ids) {
    let that = this;
    
    // 如果批量删除失败，尝试逐个删除
    let deletePromises = ids.map(id => {
        return new Promise((resolve, reject) => {
            that.$http('/doctor/deleteById', 'POST', { id: id }, true, function(resp) {
                resolve(resp);
            });
        });
    });
    
    Promise.all(deletePromises).then(() => {
        ElMessage({
            message: '操作成功',
            type: 'success',
            duration: 1200,
            onClose: () => {
                that.loadDataList();
            }
        });
    }).catch(() => {
        ElMessage({
            message: '部分删除失败',
            type: 'warning',
            duration: 1200
        });
    });
},

// 添加缺失的 selectionChangeHandle 方法
selectionChangeHandle: function(selections) {
    this.dataListSelections = selections;
},

// 添加缺失的 orderHandle 方法
orderHandle: function(column) {
    if (column.order === 'ascending') {
        this.dataForm.order = column.prop + ' ASC';
    } else if (column.order === 'descending') {
        this.dataForm.order = column.prop + ' DESC';
    } else {
        this.dataForm.order = null;
    }
    this.loadDataList();
}
    }, // methods 对象结束
    
    created: function() {
        this.loadDataList()
        this.loadMedicalDeptList()
    }
};
</script>

<style lang="less" scoped="scoped">
@import url(doctor.less);
</style>
